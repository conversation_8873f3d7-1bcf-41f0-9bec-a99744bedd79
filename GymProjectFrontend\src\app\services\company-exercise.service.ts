import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { PaginatedResult } from '../models/pagination';
import { BaseApiService } from './baseApiService';
import { SystemExerciseFilter } from './system-exercise.service';

export interface CompanyExercise {
  companyExerciseID: number;
  companyID: number;
  exerciseCategoryID: number;
  categoryName: string;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
  difficultyLevelText?: string;
  isActive?: boolean;
  creationDate?: Date;
}

export interface CompanyExerciseAdd {
  exerciseCategoryID: number;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
}

export interface CompanyExerciseUpdate {
  companyExerciseID: number;
  exerciseCategoryID: number;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
  isActive?: boolean;
}

export interface CompanyExerciseFilter {
  exerciseCategoryID?: number;
  searchTerm?: string;
  difficultyLevel?: number;
  equipment?: string;
  isActive?: boolean;
  page: number;
  pageSize: number;
}

export interface CombinedExercise {
  exerciseID: number;
  exerciseType: string; // "System" veya "Company"
  exerciseCategoryID: number;
  categoryName: string;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
  difficultyLevelText?: string;
  imagePath1?: string;
  imagePath2?: string;
  isActive?: boolean;
  creationDate?: Date;
  // Animation properties
  isAnimating?: boolean;
  currentImageIndex?: number;
}

@Injectable({
  providedIn: 'root'
})
export class CompanyExerciseService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Salon egzersizlerini getirir
   */
  getCompanyExercises(): Observable<ListResponseModel<CompanyExercise>> {
    return this.httpClient.get<ListResponseModel<CompanyExercise>>(
      `${this.apiUrl}companyexercises/getall`
    );
  }

  /**
   * Kategoriye göre salon egzersizlerini getirir
   */
  getByCategory(categoryId: number): Observable<ListResponseModel<CompanyExercise>> {
    return this.httpClient.get<ListResponseModel<CompanyExercise>>(
      `${this.apiUrl}companyexercises/getbycategory/${categoryId}`
    );
  }

  /**
   * Filtrelenmiş salon egzersizlerini sayfalı olarak getirir
   */
  getFiltered(filter: CompanyExerciseFilter): Observable<SingleResponseModel<PaginatedResult<CompanyExercise>>> {
    return this.httpClient.post<SingleResponseModel<PaginatedResult<CompanyExercise>>>(
      `${this.apiUrl}companyexercises/getfiltered`,
      filter
    );
  }

  /**
   * Salon egzersizlerinde arama yapar
   */
  search(searchTerm: string): Observable<ListResponseModel<CompanyExercise>> {
    return this.httpClient.get<ListResponseModel<CompanyExercise>>(
      `${this.apiUrl}companyexercises/search?searchTerm=${encodeURIComponent(searchTerm)}`
    );
  }

  /**
   * ID'ye göre salon egzersizi detayını getirir
   */
  getDetail(exerciseId: number): Observable<SingleResponseModel<CompanyExercise>> {
    return this.httpClient.get<SingleResponseModel<CompanyExercise>>(
      `${this.apiUrl}companyexercises/getdetail/${exerciseId}`
    );
  }

  /**
   * ID'ye göre salon egzersizi getirir
   */
  getById(exerciseId: number): Observable<SingleResponseModel<CompanyExercise>> {
    return this.httpClient.get<SingleResponseModel<CompanyExercise>>(
      `${this.apiUrl}companyexercises/getbyid/${exerciseId}`
    );
  }

  /**
   * Yeni salon egzersizi ekler
   */
  add(exercise: CompanyExerciseAdd): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}companyexercises/add`,
      exercise
    );
  }

  /**
   * Salon egzersizini günceller
   */
  update(exercise: CompanyExerciseUpdate): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}companyexercises/update`,
      exercise
    );
  }

  /**
   * Salon egzersizini siler
   */
  delete(exerciseId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}companyexercises/delete/${exerciseId}`
    );
  }

  /**
   * Birleşik egzersiz listesini getirir (Sistem + Salon egzersizleri)
   */
  getCombinedExercises(): Observable<ListResponseModel<CombinedExercise>> {
    return this.httpClient.get<ListResponseModel<CombinedExercise>>(
      `${this.apiUrl}companyexercises/getcombined`
    );
  }

  /**
   * Kategoriye göre birleşik egzersiz listesini getirir
   */
  getCombinedByCategory(categoryId: number): Observable<ListResponseModel<CombinedExercise>> {
    return this.httpClient.get<ListResponseModel<CombinedExercise>>(
      `${this.apiUrl}companyexercises/getcombinedbycategory/${categoryId}`
    );
  }

  /**
   * Filtrelenmiş birleşik egzersiz listesini sayfalı olarak getirir
   */
  getCombinedFiltered(filter: SystemExerciseFilter): Observable<SingleResponseModel<PaginatedResult<CombinedExercise>>> {
    return this.httpClient.post<SingleResponseModel<PaginatedResult<CombinedExercise>>>(
      `${this.apiUrl}companyexercises/getcombinedfiltered`,
      filter
    );
  }
}
