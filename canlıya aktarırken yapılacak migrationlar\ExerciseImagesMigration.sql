-- Exercise Images Migration Script
-- Bu script egzersiz tablolarına resim alanları ekler ve JSON verilerini import eder

-- 1. SystemExercises tablosuna resim alanları ekleme
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = 'ImagePath1')
BEGIN
    ALTER TABLE [dbo].[SystemExercises] ADD [ImagePath1] NVARCHAR(500) NULL;
    PRINT 'SystemExercises.ImagePath1 column added successfully.';
END
ELSE
BEGIN
    PRINT 'SystemExercises.ImagePath1 column already exists.';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = 'ImagePath2')
BEGIN
    ALTER TABLE [dbo].[SystemExercises] ADD [ImagePath2] NVARCHAR(500) NULL;
    PRINT 'SystemExercises.ImagePath2 column added successfully.';
END
ELSE
BEGIN
    PRINT 'SystemExercises.ImagePath2 column already exists.';
END

-- 2. CompanyExercises tablosuna resim alanları ekleme
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = 'ImagePath1')
BEGIN
    ALTER TABLE [dbo].[CompanyExercises] ADD [ImagePath1] NVARCHAR(500) NULL;
    PRINT 'CompanyExercises.ImagePath1 column added successfully.';
END
ELSE
BEGIN
    PRINT 'CompanyExercises.ImagePath1 column already exists.';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = 'ImagePath2')
BEGIN
    ALTER TABLE [dbo].[CompanyExercises] ADD [ImagePath2] NVARCHAR(500) NULL;
    PRINT 'CompanyExercises.ImagePath2 column added successfully.';
END
ELSE
BEGIN
    PRINT 'CompanyExercises.ImagePath2 column already exists.';
END

PRINT 'Exercise Images Migration completed successfully.';
