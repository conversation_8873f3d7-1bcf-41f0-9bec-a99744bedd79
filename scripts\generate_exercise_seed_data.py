#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exercise JSON to SQL Seed Data Generator
Bu script exercises klasöründeki JSON dosyalarını okuyup SQL seed data oluşturur.
"""

import json
import os
import re
from pathlib import Path

def clean_sql_string(text):
    """SQL injection'ı önlemek için string'i temizler"""
    if not text:
        return "NULL"
    # Tek tırnak karakterlerini escape et
    cleaned = text.replace("'", "''")
    return f"N'{cleaned}'"

def get_difficulty_level(level_text):
    """JSON'daki level text'ini difficulty level number'a çevirir"""
    level_map = {
        'beginner': 1,
        'intermediate': 2,
        'expert': 3
    }
    return level_map.get(level_text.lower(), 1)

def get_category_id(category_name):
    """Kategori adını kategori ID'sine çevirir"""
    category_map = {
        'strength': 1,      # <PERSON><PERSON><PERSON>
        'stretching': 2,    # Esne<PERSON>lik
        'plyometrics': 3,   # Pliometrik
        'powerlifting': 4,  # Powerlifting
        'strongman': 5,     # Strongman
        'olympic weightlifting': 6,  # Olimpik Kaldırış
        'cardio': 7,        # Kardiyovasküler
        'conditioning': 8   # Kondisyon
    }
    return category_map.get(category_name.lower(), 1)

def process_json_files():
    """JSON dosyalarını işleyip SQL seed data oluşturur"""
    exercises_dir = Path("exercises")
    
    if not exercises_dir.exists():
        print("exercises klasörü bulunamadı!")
        return
    
    sql_statements = []
    sql_statements.append("-- SystemExercises JSON Import Seed Data")
    sql_statements.append("-- Bu script exercises klasöründeki JSON verilerini SystemExercises tablosuna ekler")
    sql_statements.append("")
    sql_statements.append("BEGIN TRANSACTION;")
    sql_statements.append("")
    
    processed_count = 0
    
    # JSON dosyalarını işle
    for json_file in exercises_dir.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                exercise_data = json.load(f)
            
            # Gerekli alanları çıkar
            name = exercise_data.get('name', '')
            level = exercise_data.get('level', 'beginner')
            equipment = exercise_data.get('equipment', '')
            category = exercise_data.get('category', 'strength')
            primary_muscles = exercise_data.get('primaryMuscles', [])
            secondary_muscles = exercise_data.get('secondaryMuscles', [])
            instructions = exercise_data.get('instructions', [])
            images = exercise_data.get('images', [])
            
            # Kas gruplarını birleştir
            all_muscles = primary_muscles + secondary_muscles
            muscle_groups = ', '.join(all_muscles) if all_muscles else ''
            
            # Talimatları birleştir
            instruction_text = ' '.join(instructions) if instructions else ''
            
            # Resim yollarını ayarla
            image_path1 = f"exercises/{images[0]}" if len(images) > 0 else None
            image_path2 = f"exercises/{images[1]}" if len(images) > 1 else None
            
            # SQL statement oluştur
            sql = f"""
INSERT INTO [dbo].[SystemExercises] 
    ([ExerciseCategoryID], [ExerciseName], [Description], [Instructions], 
     [MuscleGroups], [Equipment], [DifficultyLevel], [ImagePath1], [ImagePath2], 
     [IsActive], [CreationDate])
VALUES 
    ({get_category_id(category)}, {clean_sql_string(name)}, NULL, {clean_sql_string(instruction_text)}, 
     {clean_sql_string(muscle_groups)}, {clean_sql_string(equipment)}, {get_difficulty_level(level)}, 
     {clean_sql_string(image_path1) if image_path1 else 'NULL'}, {clean_sql_string(image_path2) if image_path2 else 'NULL'}, 
     1, GETDATE());"""
            
            sql_statements.append(sql)
            processed_count += 1
            
        except Exception as e:
            print(f"Hata: {json_file} işlenirken: {e}")
            continue
    
    sql_statements.append("")
    sql_statements.append("COMMIT TRANSACTION;")
    sql_statements.append("")
    sql_statements.append(f"-- Toplam {processed_count} egzersiz eklendi")
    
    # SQL dosyasını kaydet
    output_file = Path("canlıya aktarırken yapılacak migrationlar/ExerciseJsonSeedData.sql")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sql_statements))
    
    print(f"✅ {processed_count} egzersiz işlendi")
    print(f"✅ SQL seed data oluşturuldu: {output_file}")

if __name__ == "__main__":
    process_json_files()
